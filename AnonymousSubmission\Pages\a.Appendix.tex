\newpage
\subsection*{Contents}
\begin{description}
    \item [\textbf{A}] \textbf{More Experiments} .............  \pageref{sec:app:exp}
        \begin{description}
            \item [A.1] Impact of Sampling Ratio ......... \pageref{sec:app:}
            \item [A.2] Impact of Prompts  ......... \pageref{sec:app:}
        \end{description}
    \item [\textbf{B}] \textbf{Formulas and Derivation} ........... \pageref{sec:app:formulas}
        \begin{description}
            \item[B.1] Formulas of GRPO ... \pageref{app:sec:grpo}
        \end{description}
    \item [\textbf{C}] \textbf{Additional Details about Experiment} ...... \pageref{app:sec:exp}
        \begin{description}
            \item [C.1] Comparison Methods ......................... \pageref{app:sec:details:comparison_methods}
            \item [C.2] Datasets ....................... \pageref{app:sec:details:datasets}
            \item [C.3] Running Environments ......... \pageref{app:sec:details:env}
            \item [C.4] Hyper-Parameters ......... \pageref{app:sec:details:params}
        \end{description}
    \item [\textbf{D}] \textbf{Limitation and Discussion}...\pageref{app:sec:future_work}
    \item [\textbf{E}] \textbf{Prompts Pool}...\pageref{app:sec:future_work}
\end{description}

% \begin{table}[b]
%   \centering

%   \begin{tabular}{lccc}
%     \toprule
%        Sampling Strategy      &  General QA & Multi-Hop QA & Avg. \\
%     \midrule
%     \multicolumn{3}{l}{\textbf{Search-R1}} \\
%     \hdashline
%     5 rollouts (+0\%)      &     47.2      &    19.1   & 31.2  \\
%     6 rollouts (+20\%)     &    47.6        &    19.1  & 31.3   \\
%     \midrule
%     \multicolumn{3}{l}{\textbf{REX-RAG}} \\
%     \hdashline
%     5.6 (+12\% $\leftarrow$ 12\%)     &    48.7        &    23.4    & 34.2 \\
%     5.6 (+12\% $\leftarrow$ 20\%)      &     49.5      &    30.7  & 38.7   \\
%     \bottomrule
%   \end{tabular}
%   \caption{Impact of trajectory sampling strategies on performance. Expected rollout counts shown for REX-RAG under maximum resampling scenarios (all initial outputs incorrect).}
%   \label{tab:sampling_comparison}
% \end{table}

% \subsubsection{Impact of Trajectory Sampling}

% Table~\ref{tab:sampling_comparison} analyzes the effect of different rollout configurations. Simply increasing Search-R1's rollout count from 5 to 6 (+20\% trajectories) yields minimal improvement (31.2\% to 31.3\%), demonstrating that naive oversampling is ineffective. In contrast, REX-RAG with adaptive resampling achieves substantial gains even with modest trajectory increases (5.6 expected rollouts), highlighting the importance of targeted exploration over brute-force sampling.

% \paragraph{Efficiency vs. Performance Trade-off} The comparison reveals that REX-RAG's adaptive mechanism is far more efficient than uniform oversampling. While Search-R1 requires 20\% more trajectories for negligible gains, REX-RAG achieves 23.9\% relative improvement with only 12\% additional trajectories, demonstrating superior sample efficiency. Furthermore, the computational overhead scales linearly with the resampling parameter $p$, allowing practitioners to trade off between performance gains and computational cost based on their specific requirements and resource constraints.
% \begin{table}[b]
%   \centering

%   \begin{tabular}{lccc}
%     \toprule
%        Sampling Strategy      &  General QA & Multi-Hop QA & Avg. \\
%     \midrule
%     \multicolumn{3}{l}{\textbf{Search-R1}} \\
%     \hdashline
%     5 rollouts (+0\%)      &     47.2      &    19.1   & 31.2  \\
%     6 rollouts (+20\%)     &    47.6        &    19.1  & 31.3   \\
%     \midrule
%     \multicolumn{3}{l}{\textbf{REX-RAG}} \\
%     \hdashline
%     5.6 (+12\% $\leftarrow$ 12\%)     &    48.7        &    23.4    & 34.2 \\
%     5.6 (+12\% $\leftarrow$ 20\%)      &     49.5      &    30.7  & 38.7   \\
%     \bottomrule
%   \end{tabular}
%   \caption{Impact of trajectory sampling strategies on performance. Expected rollout counts shown for REX-RAG under maximum resampling scenarios (all initial outputs incorrect).}
%   \label{tab:sampling_comparison}
% \end{table}

% \subsubsection{Impact of Trajectory Sampling}

% Table~\ref{tab:sampling_comparison} analyzes the effect of different rollout configurations. Simply increasing Search-R1's rollout count from 5 to 6 (+20\% trajectories) yields minimal improvement (31.2\% to 31.3\%), demonstrating that naive oversampling is ineffective. In contrast, REX-RAG with adaptive resampling achieves substantial gains even with modest trajectory increases (5.6 expected rollouts), highlighting the importance of targeted exploration over brute-force sampling.

% \paragraph{Efficiency vs. Performance Trade-off} The comparison reveals that REX-RAG's adaptive mechanism is far more efficient than uniform oversampling. While Search-R1 requires 20\% more trajectories for negligible gains, REX-RAG achieves 23.9\% relative improvement with only 12\% additional trajectories, demonstrating superior sample efficiency. Furthermore, the computational overhead scales linearly with the resampling parameter $p$, allowing practitioners to trade off between performance gains and computational cost based on their specific requirements and resource constraints.

